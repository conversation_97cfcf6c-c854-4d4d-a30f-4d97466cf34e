---
# Service Restart Task - Robust and Idempotent
# Supports both group-based and individual service restart
# Usage:
#   - restart_groups: ['recognize', 'api']  # Restart services by group
#   - restart_services: ['nginx', 'php-fpm-74']  # Restart specific services
#   - At least one of restart_groups or restart_services must be specified

# Ensure timestamp is available for logging (common timestamp handling)
- name: Set fallback timestamp for logging
  ansible.builtin.set_fact:
    ansible_date_time:
      iso8601: >-
        {{ lookup('pipe', 'date -u +"%Y-%m-%dT%H:%M:%SZ"') }}
      epoch: "{{ lookup('pipe', 'date +%s') }}"
  when: ansible_date_time is not defined
  changed_when: false

# Set default variables
- name: Set default variables for service restart
  ansible.builtin.set_fact:
    remote_logs_dir: "{{ services_info.restart.defaults.remote_logs_dir | default('/tmp/ansible-service-restart-logs') }}"
    execution_timeout: "{{ services_info.restart.defaults.execution_timeout | default(-1) }}"
    max_retry_attempts: "{{ services_info.restart.defaults.max_retry_attempts | default(3) }}"
    retry_delay: "{{ services_info.restart.defaults.retry_delay | default(10) }}"
    restart_groups: "{{ restart_groups | default([]) }}"
    restart_services: "{{ restart_services | default([]) }}"

# Validate input parameters
- name: Validate input parameters
  ansible.builtin.fail:
    msg: |
      Invalid input parameters:
      - At least one of 'restart_groups' or 'restart_services' must be specified
      - restart_groups: {{ restart_groups }}
      - restart_services: {{ restart_services }}
  when:
    - restart_groups | length == 0
    - restart_services | length == 0

# Build service restart list from groups
- name: Initialize services from groups
  ansible.builtin.set_fact:
    services_from_groups: []
  when: restart_groups | length > 0

- name: Collect service names from groups
  ansible.builtin.set_fact:
    group_service_names: []
  when: restart_groups | length > 0

- name: Add service names from each group
  ansible.builtin.set_fact:
    group_service_names: >-
      {{
        group_service_names + services_info.restart.groups[item]
      }}
  loop: "{{ restart_groups }}"
  when:
    - restart_groups | length > 0
    - item in services_info.restart.groups

- name: Remove duplicate service names
  ansible.builtin.set_fact:
    group_service_names: "{{ group_service_names | unique }}"
  when: restart_groups | length > 0

- name: Build service configurations from group service names
  ansible.builtin.set_fact:
    services_from_groups: >-
      {{
        services_from_groups +
        [services_info.restart.services[item] | combine({'service': item})]
      }}
  loop: "{{ group_service_names }}"
  when:
    - restart_groups | length > 0
    - item in services_info.restart.services

# Build service restart list from individual services
- name: Build individual services list
  ansible.builtin.set_fact:
    services_from_individual: []
  when: restart_services | length > 0

- name: Add individual services to list
  ansible.builtin.set_fact:
    services_from_individual: >-
      {{
        services_from_individual +
        [services_info.restart.services[item] | combine({'service': item})]
      }}
  loop: "{{ restart_services }}"
  when:
    - restart_services | length > 0
    - item in services_info.restart.services

# Combine and deduplicate service lists
- name: Combine and deduplicate service lists
  ansible.builtin.set_fact:
    final_service_list: >-
      {{
        (services_from_groups | default([])) +
        (services_from_individual | default([])) |
        unique
      }}

# Filter services for current host
- name: Filter services for current host
  ansible.builtin.set_fact:
    final_service_list: >-
      {{
        final_service_list |
        selectattr('host', 'equalto', ansible_host | default(inventory_hostname)) |
        list
      }}

# Display services to be restarted
- name: Display services to be restarted
  ansible.builtin.debug:
    msg: |
      Service Restart Plan:
      ====================
      Total services to restart: {{ final_service_list | length }}

      Services by host:
      {% for host in final_service_list | map(attribute='host') | unique %}
      Host: {{ host }}
      {% for service in final_service_list | selectattr('host', 'equalto', host) %}
        - {{ service.service }} ({{ service.path }})
      {% endfor %}
      {% endfor %}

# Create remote logs directory
- name: Create remote logs directory
  ansible.builtin.file:
    path: "{{ remote_logs_dir }}"
    state: directory
    mode: "0755"
  become: false

# Pre-check: Verify docker-compose files exist
- name: Pre-check docker-compose files
  ansible.builtin.shell: |
    cd "{{ item }}"
    # Check for docker-compose.yml or docker-compose-*.yml files
    if ls docker-compose*.yml >/dev/null 2>&1; then
      echo "FOUND"
    else
      echo "NOT_FOUND"
    fi
  register: compose_file_check
  loop: "{{ final_service_list | map(attribute='path') | unique }}"
  failed_when: false
  changed_when: false

# Report missing docker-compose files
- name: Report missing docker-compose files
  ansible.builtin.debug:
    msg: |
      WARNING: No docker-compose*.yml files found at {{ item.item }}
      This may cause service restart to fail.
  loop: "{{ compose_file_check.results }}"
  when: item.stdout == "NOT_FOUND"

# Create restart execution log
- name: Create restart execution log
  ansible.builtin.lineinfile:
    path: "{{ remote_logs_dir }}/service-restart-execution.log"
    line: >-
      {{ ansible_date_time.iso8601 }} - CONTROLLER - SERVICE_RESTART - STARTED -
      Groups: {{ restart_groups | join(',') }} Services: {{ restart_services | join(',') }}
    create: true
    mode: "0644"
  become: false

# Record pre-restart service status
- name: Record pre-restart service status
  ansible.builtin.shell: |
    set -euo pipefail
    cd "{{ item.path }}"
    LOG_FILE="{{ remote_logs_dir }}/service-status-{{ item.service }}-{{ ansible_date_time.epoch }}.log"
    echo "=== Pre-restart Status for {{ item.service }} ===" >> "$LOG_FILE"
    echo "Timestamp: {{ ansible_date_time.iso8601 }}" >> "$LOG_FILE"
    echo "Host: {{ item.host }} ({{ ansible_hostname }})" >> "$LOG_FILE"
    echo "Path: {{ item.path }}" >> "$LOG_FILE"
    echo "Service: {{ item.service }}" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"

    # Check if docker-compose files exist
    if ls docker-compose*.yml >/dev/null 2>&1; then
      echo "docker-compose files: EXISTS" >> "$LOG_FILE"
      ls docker-compose*.yml >> "$LOG_FILE" 2>&1

      # Get current service status
      if docker-compose ps {{ item.service }} >> "$LOG_FILE" 2>&1; then
        echo "Service status check: SUCCESS" >> "$LOG_FILE"
      else
        echo "Service status check: FAILED" >> "$LOG_FILE"
      fi
    else
      echo "docker-compose files: NOT FOUND" >> "$LOG_FILE"
    fi
    echo "" >> "$LOG_FILE"
  register: pre_restart_status
  loop: "{{ final_service_list }}"
  become: false
  failed_when: false
  changed_when: true
  args:
    executable: /bin/bash

# Execute service restart with retry logic
- name: Execute service restart with retry
  vars:
    timeout_cmd: "{% if execution_timeout | int != -1 %}timeout {{ execution_timeout }}{% else %}{% endif %}"
    use_timeout: "{{ execution_timeout | int != -1 }}"
  ansible.builtin.shell: |
    set -euo pipefail
    cd "{{ item.path }}"
    LOG_FILE="{{ remote_logs_dir }}/service-restart-{{ item.service }}-{{ ansible_date_time.epoch }}.log"

    echo "=== Service Restart Execution ===" >> "$LOG_FILE"
    echo "Timestamp: {{ ansible_date_time.iso8601 }}" >> "$LOG_FILE"
    echo "Service: {{ item.service }}" >> "$LOG_FILE"
    echo "Command: {{ item.command }}" >> "$LOG_FILE"
    echo "Timeout: {% if execution_timeout | int != -1 %}{{ execution_timeout }} seconds{% else %}No timeout{% endif %}" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"

    # Execute restart command with conditional timeout
    {% if use_timeout %}
    if {{ timeout_cmd }} {{ item.command }} >> "$LOG_FILE" 2>&1; then
    {% else %}
    if {{ item.command }} >> "$LOG_FILE" 2>&1; then
    {% endif %}
      RESTART_EXIT_CODE=0
      echo "Service restart: SUCCESS" >> "$LOG_FILE"
    else
      RESTART_EXIT_CODE=$?
      echo "Service restart: FAILED (Exit Code: $RESTART_EXIT_CODE)" >> "$LOG_FILE"
    fi

    # Wait a moment for service to stabilize
    sleep 5

    # Verify service status after restart
    echo "" >> "$LOG_FILE"
    echo "=== Post-restart Status Verification ===" >> "$LOG_FILE"
    if docker-compose ps {{ item.service }} >> "$LOG_FILE" 2>&1; then
      echo "Post-restart status check: SUCCESS" >> "$LOG_FILE"
    else
      echo "Post-restart status check: FAILED" >> "$LOG_FILE"
      RESTART_EXIT_CODE=1
    fi

    exit $RESTART_EXIT_CODE
  register: service_restart_result
  loop: "{{ final_service_list }}"
  become: false
  retries: "{{ max_retry_attempts }}"
  delay: "{{ retry_delay }}"
  args:
    executable: /bin/bash
  failed_when: service_restart_result.rc != 0
  changed_when: service_restart_result.rc == 0

# Log successful restarts
- name: Log successful service restarts
  ansible.builtin.lineinfile:
    path: "{{ remote_logs_dir }}/service-restart-execution.log"
    line: "{{ ansible_date_time.iso8601 }} - {{ item.item.host }} - {{ item.item.service }} - SUCCESS - Path: {{ item.item.path }}"
    mode: "0644"
  become: false
  loop: "{{ service_restart_result.results }}"
  when: item.rc == 0

# Log failed restarts
- name: Log failed service restarts
  ansible.builtin.lineinfile:
    path: "{{ remote_logs_dir }}/service-restart-execution.log"
    line: "{{ ansible_date_time.iso8601 }} - {{ item.item.host }} - {{ item.item.service }} - FAILED - Path: {{ item.item.path }} - Exit Code: {{ item.rc }}"
    mode: "0644"
  become: false
  loop: "{{ service_restart_result.results }}"
  when: item.rc != 0

# Generate restart summary report
- name: Generate restart summary report
  ansible.builtin.set_fact:
    restart_summary: |
      Service Restart Summary Report
      ==============================
      Execution Time: {{ ansible_date_time.iso8601 }}
      Target Hosts: {{ final_service_list | map(attribute='host') | unique | join(', ') }}

      Input Parameters:
      - Restart Groups: {{ restart_groups | join(', ') if restart_groups | length > 0 else 'None' }}
      - Restart Services: {{ restart_services | join(', ') if restart_services | length > 0 else 'None' }}

      Execution Results:
      - Total Services: {{ final_service_list | length }}
      - Successful: {{ service_restart_result.results | selectattr('rc', 'equalto', 0) | list | length }}
      - Failed: {{ service_restart_result.results | rejectattr('rc', 'equalto', 0) | list | length }}

      Service Details:
      {% for result in service_restart_result.results %}
      - {{ result.item.service }} ({{ result.item.host }}): {{ 'SUCCESS' if result.rc == 0 else 'FAILED (Exit Code: ' + result.rc|string + ')' }}
      {% endfor %}

      Log Files Location: {{ remote_logs_dir }}

# Display restart summary
- name: Display service restart summary
  ansible.builtin.debug:
    msg: "{{ restart_summary }}"

# Handle partial or complete failures
- name: Handle service restart failures
  ansible.builtin.fail:
    msg: |
      Service restart completed with failures:

      {{ restart_summary }}

      Failed Services:
      {% for result in service_restart_result.results %}
      {% if result.rc != 0 %}
      - {{ result.item.service }} on {{ result.item.host }}: Exit Code {{ result.rc }}
        Command: {{ result.item.command }}
        Path: {{ result.item.path }}
        Error: {{ result.stderr | default('No error output') }}
      {% endif %}
      {% endfor %}

      Check detailed logs at: {{ remote_logs_dir }}
  when: service_restart_result.results | rejectattr('rc', 'equalto', 0) | list | length > 0

# Log completion
- name: Log restart completion
  ansible.builtin.lineinfile:
    path: "{{ remote_logs_dir }}/service-restart-execution.log"
    line: >-
      {{ ansible_date_time.iso8601 }} - CONTROLLER - SERVICE_RESTART - COMPLETED -
      Success: {{ service_restart_result.results | selectattr('rc', 'equalto', 0) | list | length }}/{{ final_service_list | length }}
    mode: "0644"
  become: false

# Clean up old log files (keep last 20 executions)
- name: Clean up old restart log files
  ansible.builtin.shell: |
    cd "{{ remote_logs_dir }}"
    # Keep only the 20 most recent execution log files
    ls -1t service-restart-execution.log.* 2>/dev/null | tail -n +21 | xargs -r rm -f
    # Keep only the 20 most recent service status log files
    ls -1t service-status-*.log 2>/dev/null | tail -n +21 | xargs -r rm -f
    # Keep only the 20 most recent service restart log files
    ls -1t service-restart-*.log 2>/dev/null | tail -n +21 | xargs -r rm -f
  become: false
  changed_when: false
  failed_when: false
