---
- name: Check if conda is installed
  ansible.builtin.stat:
    path: "{{ PERF.home_path }}/{{ PERF.locust.conda.relative_conda_path }}"
  register: conda_stat

- name: Verify conda installation
  ansible.builtin.command: "{{ PERF.home_path }}/{{ PERF.locust.conda.relative_conda_path }} --version"
  register: conda_version
  when: conda_stat.stat.exists
  changed_when: false
  failed_when: false

- name: Install miniconda3
  when:
    - ansible_host in groups.generator
    - not conda_stat.stat.exists or conda_version.rc != 0
  notify:
    - Del miniconda3 installation script
    - Reset SSH Connection
  block:
    - name: Transport auto-install-miniconda3.sh script
      ansible.builtin.template:
        src: "{{ item }}.j2"
        dest: "{{ PERF.home_path }}/{{ item }}"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0664"
        backup: true
      loop:
        - auto-install-miniconda3.sh
      register: transport_result

    - name: Execute miniconda3 installation script
      ansible.builtin.command:
        cmd: "bash {{ PERF.home_path }}/auto-install-miniconda3.sh"
        chdir: "{{ PERF.home_path }}"
      when: transport_result is success

    - name: Ensure miniconda3 directory permissions and ownership
      ansible.builtin.file:
        path: "{{ PERF.home_path }}/miniconda3"
        state: directory
        mode: "0755"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        recurse: true

- name: Flush handlers
  ansible.builtin.meta: flush_handlers

- name: Validate git installation
  block:
    - name: Check if git is installed
      ansible.builtin.command: git --version
      register: git_installed
      changed_when: false
      failed_when: git_installed.rc != 0

  rescue:
    - name: Install git package
      ansible.builtin.apt:
        name: git
        state: present
        update_cache: true
        cache_valid_time: 3600
      become: true

- name: Manage repository and SSH setup
  block:
    - name: Check if testing repository already exists
      ansible.builtin.stat:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
      register: repo_stat

  rescue:
    - name: Setup SSH keys for repository access
      ansible.builtin.copy:
        src: "{{ item[0] }}"
        dest: "{{ PERF.home_path }}/.ssh/{{ item[0] | basename }}"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "{{ item[1] }}"
        force: true
        backup: true
      loop:
        - ["keys/{{ PERF.repo.perf.ssh_key_name }}", "0600"]
        - ["keys/{{ PERF.repo.perf.ssh_key_name }}.pub", "0644"]
        - ["keys/config", "0600"]

    - name: Ensure repository parent directory exists
      ansible.builtin.file:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}"
        state: directory
        mode: "0755"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"

    - name: Clone the target repository
      ansible.builtin.git:
        repo: "{{ PERF.repo.perf.address }}"
        dest: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
        accept_hostkey: true
        version: "{{ PERF.repo.perf.target_branch | default('main') }}"
      environment:
        GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"

  always:
    - name: Ensure .ssh directory permissions
      ansible.builtin.file:
        path: "{{ PERF.home_path }}/.ssh"
        state: directory
        mode: "0700"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"

    - name: Check latest repository status
      ansible.builtin.stat:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
      register: latest_repo_stat

    - name: Manage existing repository
      when: latest_repo_stat.stat.exists
      block:
        - name: Check current branch
          ansible.builtin.command: "git branch --show-current"
          args:
            chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
          register: current_branch
          environment:
            GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
          changed_when: false
          failed_when: false

        - name: Switch to target branch if not already on it
          ansible.builtin.command: "git switch {{ PERF.repo.perf.target_branch | default('main') }}"
          args:
            chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
          environment:
            GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
          when: current_branch.stdout != PERF.repo.perf.target_branch | default('main')

        - name: Pull latest changes if repository exists
          ansible.builtin.git:
            repo: "{{ PERF.repo.perf.address }}"
            dest: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
            update: true
            accept_hostkey: true
            force: true
          environment:
            GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
          ignore_errors: true

        - name: Ensure repository directory permissions and ownership
          ansible.builtin.file:
            path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}"
            state: directory
            mode: "0755"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            recurse: true

- name: Create locust conda environment
  ansible.builtin.command: >
    {{ PERF.home_path }}/{{ PERF.locust.conda.relative_conda_path }}
    create -n locust_v{{ PERF.locust.conda.locust_version }}
    python={{ PERF.locust.conda.python_version }} -y
  register: conda_env_result
  changed_when: "'already exists' not in conda_env_result.stderr"
  failed_when:
    - conda_env_result.rc != 0
    - "'already exists' not in conda_env_result.stderr"

- name: Install locust requirements in conda environment
  ansible.builtin.command:
    chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}/{{ PERF.locust.requirements.relative_dir_path_in_repo }}"
    cmd: >
      {{ PERF.home_path }}/miniconda3/envs/locust_v{{ PERF.locust.conda.locust_version }}/bin/python
      -m pip install -r {{ item }}
  loop:
    - locust_v{{ PERF.locust.conda.locust_version }}_requirements.txt
    - requirements.dev.txt
    - requirements.locust.backend.txt
  register: pip_install_result
  changed_when: "'already satisfied' not in pip_install_result.stdout"
