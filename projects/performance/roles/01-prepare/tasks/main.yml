---
- name: Update and upgrade system packages
  ansible.builtin.import_tasks: task-apt_update_upgrade.yml
  tags:
    - task_apt_u_u
    - system_update

# https://docs.docker.com/engine/install/ubuntu/
- name: Install Docker and dependencies
  ansible.builtin.import_tasks: task-install_docker_dep.yml
  tags:
    - task_install_docker_dep
    - docker_install

- name: Install Python3 pip
  ansible.builtin.import_tasks: task-install_pip3.yml
  tags:
    - task_install_pip3
    - python_tools