---
- name: Check if docker is installed
  ansible.builtin.command: docker --version
  register: docker_installed
  ignore_errors: true
  changed_when: false

- name: Get OS information
  ansible.builtin.setup:
    filter: ansible_distribution*
  when: docker_installed.rc != 0

- name: Install Docker prerequisites
  ansible.builtin.apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common
      - gnupg
    state: present
    update_cache: true
    cache_valid_time: 3600
  when: docker_installed.rc != 0
  become: true

- name: Add Docker's official GPG key and repository
  when: docker_installed.rc != 0
  become: true
  block:
    - name: Download Docker GPG key
      ansible.builtin.get_url:
        url: "https://download.docker.com/linux/{{ ansible_distribution | lower }}/gpg"
        dest: /usr/share/keyrings/docker-archive-keyring.gpg.asc
        mode: "0644"
        force: true
      register: gpg_key
      retries: 5
      delay: 5
      until: gpg_key is success

    - name: Trigger GPG key conversion
      ansible.builtin.debug:
        msg: "GPG key downloaded, will convert to binary format"
      notify:
        - Convert GPG key to binary format
        - Remove temporary ASCII GPG key file
      when: gpg_key.changed

    - name: Add Docker APT repository
      ansible.builtin.apt_repository:
        repo: >-
          deb [arch={{ ansible_architecture }} signed-by=/usr/share/keyrings/docker-archive-keyring.gpg]
          https://download.docker.com/linux/{{ ansible_distribution | lower }}
          {{ ansible_distribution_release }} stable
        state: present
        filename: docker
        update_cache: true
      register: repo_added

    - name: Install Docker packages
      ansible.builtin.apt:
        name:
          - docker-ce
          - docker-ce-cli
          - containerd.io
          - docker-buildx-plugin
          - docker-compose-plugin
        state: present
        update_cache: true
      when: repo_added.changed or gpg_key.changed

- name: Ensure Docker service is started and enabled
  ansible.builtin.systemd:
    name: docker
    state: started
    enabled: true
  become: true

- name: Add user to docker group (optional, for non-root access)
  ansible.builtin.user:
    name: "{{ ansible_user | default('ubuntu') }}"
    groups: docker
    append: true
  register: user_group
  when: ansible_user != 'root'
  become: true

- name: Trigger SSH connection reset
  ansible.builtin.debug:
    msg: "User added to docker group, will reset SSH connection"
  notify: Reset SSH connection to apply group changes
  when: user_group.changed

- name: Test Docker installation
  ansible.builtin.command:
    cmd: docker run --rm hello-world
  register: docker_test
  changed_when: false
  failed_when: docker_test.rc != 0
  when: user_group is success or ansible_user is not defined

- name: Check if docker-compose is installed
  ansible.builtin.command: docker-compose --version
  register: docker_compose_installed
  ignore_errors: true
  changed_when: false

- name: Install docker-compose
  ansible.builtin.get_url:
    url: "https://github.com/docker/compose/releases/download/v{{ PERF.dc_version }}/docker-compose-{{ ansible_system | lower }}-{{ ansible_architecture }}"
    dest: /usr/local/bin/docker-compose
    mode: "0755"
    owner: root
    group: root
  register: docker_compose_download
  when: docker_compose_installed.rc != 0
  become: true
  retries: 3
  delay: 5

- name: Verify docker-compose installation
  ansible.builtin.assert:
    that:
      - docker_compose_download.dest is defined
      - docker_compose_download.status_code == 200
    fail_msg: "Failed to download docker-compose: {{ docker_compose_download.msg | default('Unknown error') }}"
    success_msg: "Docker-compose installed successfully"
  when: docker_compose_installed.rc != 0

- name: Verify docker-compose is executable
  ansible.builtin.command: docker-compose --version
  register: docker_compose_verify
  changed_when: false
  when: docker_compose_installed.rc != 0
