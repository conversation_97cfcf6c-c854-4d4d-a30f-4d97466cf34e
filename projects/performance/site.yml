---
- name: "Phase 1: Prepare infrastructure and dependencies"
  hosts: generator
  gather_facts: true
  roles:
    - role: 01-prepare
      tags: [infrastructure, prepare]
  tags:
    - play_prepare
    - deploy_all

- name: "Phase 2: Deploy and configure generator services"
  hosts: generator
  gather_facts: false
  roles:
    - role: 02-generator
      tags: [services, generator]
  tags:
    - play_generator
    - deploy_all

- name: "Phase 3: Execute Locust performance tests"
  hosts: generator
  gather_facts: false
  roles:
    - role: 03-locust
      tags: [testing, locust]
  tags:
    - never
    - locust_run

- name: Display usage instructions
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display usage instructions
      debug:
        msg: |
          Performance Test Script Execution Usage:
          =======================================
          Available top-level tags for plays:
          - play_prepare: Installs required dependencies on the generator.
          - play_generator: Deploys and configures the test generator services.
          - locust_run: Executes Locust tasks (start, stop, backup).
          - deploy_all: Runs both play_prepare and play_generator.

          Locust Task Execution (via `locust_run` tag):
          ---------------------------------------------
          The `03-locust` role is controlled by the `run_type` variable.

          Required variables:
          - run_type: Defines the action. Can be 'start', 'stop', or 'backup'.
          - extra-vars: You must provide a scenario definition file from `extra-vars/locust/`.

          Optional boolean variables for 'start' run_type:
          - standalone.sync_data: (default: false) Set to true to sync test data from the repo.
          - standalone.build_image: (default: false) Set to true to rebuild the locust docker image.
          - standalone.debug: (default: false) Set to true to display live container logs.

          Tags for locust tasks:
          - standalone / locust_standalone: To run locust in standalone mode.

          Examples:
          # Run all preparation and deployment steps
          ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-sde.yml"

          # Using the extra-vars file directly in the command line to start a standalone locust test
          ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml"

          # Start a standalone locust test with debugging enabled
          ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=start" -e "standalone.debug=true"

          # Stop a running test
          ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=stop"

          # Backup test results
          ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=backup"
  tags:
    - never
    - usage
